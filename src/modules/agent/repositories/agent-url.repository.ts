import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { AgentUrl } from '@modules/agent/entities';

/**
 * Repository cho AgentUrl
 * Xử lý các thao tác CRUD và truy vấn nâng cao liên quan đến URL của agent
 */
@Injectable()
export class AgentUrlRepository extends Repository<AgentUrl> {
  private readonly logger = new Logger(AgentUrlRepository.name);

  constructor(private dataSource: DataSource) {
    super(AgentUrl, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho AgentUrl
   * @returns SelectQueryBuilder cho AgentUrl
   */
  private createBaseQuery(): SelectQueryBuilder<AgentUrl> {
    return this.createQueryBuilder('agentUrl');
  }

  /**
   * Tìm URL của agent theo ID
   * @param id ID của URL
   * @returns AgentUrl nếu tìm thấy, null nếu không tìm thấy
   */
  async findById(id: number): Promise<AgentUrl | null> {
    return this.createBaseQuery()
      .where('agentUrl.id = :id', { id })
      .getOne();
  }

  /**
   * Tìm URL của agent theo ID agent
   * @param agentId ID của agent
   * @returns Danh sách URL của agent
   */
  async findByAgentId(agentId: string): Promise<AgentUrl[]> {
    return this.createBaseQuery()
      .where('agentUrl.agentId = :agentId', { agentId })
      .getMany();
  }

  /**
   * Tìm URL của agent theo ID URL
   * @param urlId ID của URL
   * @returns Danh sách URL của agent
   */
  async findByUrlId(urlId: number): Promise<AgentUrl[]> {
    return this.createBaseQuery()
      .where('agentUrl.urlId = :urlId', { urlId })
      .getMany();
  }

  /**
   * Xóa URL của agent theo ID agent và ID URL
   * @param agentId ID của agent
   * @param urlId ID của URL
   * @returns Số lượng bản ghi đã bị xóa
   */
  async deleteByAgentIdAndUrlId(agentId: string, urlId: number): Promise<number> {
    const result = await this.createQueryBuilder()
      .delete()
      .from(AgentUrl)
      .where('agentId = :agentId AND urlId = :urlId', { agentId, urlId })
      .execute();
    
    return result.affected || 0;
  }
}
