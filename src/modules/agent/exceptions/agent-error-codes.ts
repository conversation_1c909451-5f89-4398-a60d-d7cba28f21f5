import { ErrorCode } from '@common/exceptions/app.exception';
import { HttpStatus } from '@nestjs/common';

/**
 * Agent Error Codes (Range: 10100-10199)
 */
export const AGENT_ERROR_CODES = {
  AGENT_NOT_FOUND: new ErrorCode(10100, 'Không tìm thấy agent', HttpStatus.NOT_FOUND),
  AGENT_ALREADY_EXISTS: new ErrorCode(10101, 'Agent đã tồn tại', HttpStatus.CONFLICT),
  INVALID_AGENT_CONFIG: new ErrorCode(10102, 'Cấu hình agent không hợp lệ', HttpStatus.BAD_REQUEST),
  AGENT_CREATION_FAILED: new ErrorCode(10103, 'Tạo agent thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  AGENT_UPDATE_FAILED: new ErrorCode(10104, '<PERSON><PERSON><PERSON> nhật agent thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  AGENT_DELETE_FAILED: new ErrorCode(10105, 'Xóa agent thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  INSUFFICIENT_PERMISSIONS: new ErrorCode(10106, 'Không đủ quyền truy cập', HttpStatus.FORBIDDEN),

  // Model Resolution Error Codes (10107-10120)
  INVALID_KEY_LLM_ID: new ErrorCode(10107, 'Key LLM ID không hợp lệ hoặc không tồn tại', HttpStatus.BAD_REQUEST),
  MODEL_REGISTRY_NOT_FOUND: new ErrorCode(10108, 'Không tìm thấy model registry phù hợp', HttpStatus.NOT_FOUND),
  MODEL_BASE_NOT_FOUND: new ErrorCode(10109, 'Không tìm thấy model base active', HttpStatus.NOT_FOUND),
  MODEL_PROVIDER_MISMATCH: new ErrorCode(10110, 'Provider của model và key LLM không khớp', HttpStatus.BAD_REQUEST),
  MODEL_NOT_ACCESSIBLE: new ErrorCode(10111, 'Model không được phép truy cập', HttpStatus.FORBIDDEN),
  INVALID_MODEL_NAME_FORMAT: new ErrorCode(10112, 'Định dạng tên model không hợp lệ', HttpStatus.BAD_REQUEST),
  MODEL_RESOLUTION_FAILED: new ErrorCode(10113, 'Không thể resolve model', HttpStatus.INTERNAL_SERVER_ERROR),
  CANNOT_DELETE_ACTIVE_AGENT: new ErrorCode(10114, 'Không thể xóa agent đang active', HttpStatus.BAD_REQUEST),

  // Integration Error Codes (10115-10120)
  FACEBOOK_PAGE_NOT_FOUND: new ErrorCode(10115, 'Không tìm thấy Facebook page', HttpStatus.NOT_FOUND),
  WEBSITE_NOT_FOUND: new ErrorCode(10116, 'Không tìm thấy website', HttpStatus.NOT_FOUND),
};
