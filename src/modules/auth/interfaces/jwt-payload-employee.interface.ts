import { TokenType } from '../guards/jwt.util';

/**
 * Interface định nghĩa payload của JWT token cho nhân viên
 */
export interface JWTPayloadEmployee {
  /**
   * ID của nhân viên
   */
  id: number;
  
  /**
   * Subject (thường là ID của nhân viên)
   */
  sub: number;
  
  /**
   * Tên đăng nhập của nhân viên (tùy chọn)
   */
  username?: string;
  
  /**
   * Danh sách quyền của nhân viên (tùy chọn)
   */
  permissions?: string[];
  
  /**
   * <PERSON><PERSON><PERSON> token (tùy chọn cho khả năng tương thích ngược)
   */
  typeToken?: TokenType;
  
  /**
   * Cờ đánh dấu là admin (tùy chọn cho khả năng tương thích ngược)
   */
  isAdmin?: boolean;
  
  /**
   * Thời gian hết hạn của token
   */
  exp?: number;
  
  /**
   * Thời gian tạo token
   */
  iat?: number;
}
