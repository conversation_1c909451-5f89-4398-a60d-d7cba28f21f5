import { CurrentEmployee } from '@/modules/auth/decorators';
import { JwtEmployeeGuard } from '@/modules/auth/guards';
import { ApiResponseDto } from '@common/response';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags
} from '@nestjs/swagger';
import { PermissionsGuard } from '@/modules/auth/guards/permissions.guard';
import {
  CreateModelRegistryDto,
  UpdateModelRegistryDto,
  ModelRegistryQueryDto
} from '../dto/model-registry';
import { AdminModelRegistryService } from '../services';
import { SWAGGER_API_TAGS } from '@/common/swagger';

/**
 * Controller xử lý API cho Admin Model Registry
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_MODEL_REGISTRY)
@Controller('admin/model-registry')
@UseGuards(JwtEmployeeGuard, PermissionsGuard)
@ApiBearerAuth('JWT-auth')
export class AdminModelRegistryController {
  constructor(private readonly adminModelRegistryService: AdminModelRegistryService) { }

  // /**
  //  * Tạo mới model registry
  //  */
  // @Post()
  // @ApiOperation({ summary: 'Tạo mới model registry' })
  // @ApiResponse({
  //   status: 201,
  //   description: 'Tạo mới model registry thành công',
  //   type: ApiResponseDto
  // })
  // create(
  //   @Body() createDto: CreateModelRegistryDto,
  //   @CurrentEmployee('id') employeeId: number
  // ) {
  //   return this.adminModelRegistryService.create(createDto, employeeId);
  // }

  // /**
  //  * Lấy danh sách model registry có phân trang và tìm kiếm
  //  */
  // @Get()
  // @ApiOperation({
  //   summary: 'Lấy danh sách model registry có phân trang và tìm kiếm',
  //   description: 'API này hỗ trợ tìm kiếm theo tên pattern, phân trang và sắp xếp'
  // })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Danh sách model registry',
  //   type: ApiResponseDto
  // })
  // findAll(@Query() queryDto: ModelRegistryQueryDto) {
  //   return this.adminModelRegistryService.findAll(queryDto);
  // }

  // /**
  //  * Lấy chi tiết model registry
  //  */
  // @Get(':id')
  // @ApiOperation({ summary: 'Lấy chi tiết model registry' })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Chi tiết model registry',
  //   type: ApiResponseDto
  // })
  // findOne(@Param('id') id: string) {
  //   return this.adminModelRegistryService.findOne(id);
  // }

  // /**
  //  * Cập nhật model registry
  //  */
  // @Patch(':id')
  // @ApiOperation({ summary: 'Cập nhật model registry' })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Cập nhật model registry thành công',
  //   type: ApiResponseDto
  // })
  // update(
  //   @Param('id') id: string,
  //   @Body() updateDto: UpdateModelRegistryDto,
  //   @CurrentEmployee('id') employeeId: number
  // ) {
  //   return this.adminModelRegistryService.update(id, updateDto, employeeId);
  // }

  // /**
  //  * Xóa model registry (soft delete)
  //  */
  // @Delete(':id')
  // @ApiOperation({ summary: 'Xóa model registry' })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Xóa model registry thành công',
  //   type: ApiResponseDto
  // })
  // remove(
  //   @Param('id') id: string,
  //   @CurrentEmployee('id') employeeId: number
  // ) {
  //   return this.adminModelRegistryService.remove(id, employeeId);
  // }

  // /**
  //  * Lấy danh sách model registry đã xóa
  //  */
  // @Get('deleted/list')
  // @ApiOperation({ summary: 'Lấy danh sách model registry đã xóa' })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Danh sách model registry đã xóa',
  //   type: ApiResponseDto
  // })
  // findDeleted(@Query() queryDto: ModelRegistryQueryDto) {
  //   return this.adminModelRegistryService.findDeleted(queryDto);
  // }

  // /**
  //  * Khôi phục model registry đã xóa
  //  */
  // @Patch(':id/restore')
  // @ApiOperation({ summary: 'Khôi phục model registry đã xóa' })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Khôi phục model registry thành công',
  //   type: ApiResponseDto
  // })
  // restore(
  //   @Param('id') id: string,
  //   @CurrentEmployee('id') employeeId: number
  // ) {
  //   return this.adminModelRegistryService.restore(id, employeeId);
  // }

  // /**
  //  * Test pattern matching với model name
  //  */
  // @Post('test-pattern')
  // @ApiOperation({ summary: 'Test pattern matching với model name' })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Kết quả test pattern matching',
  //   type: ApiResponseDto
  // })
  // testPattern(
  //   @Body() testData: { pattern: string; modelName: string }
  // ) {
  //   return this.adminModelRegistryService.testPattern(testData.pattern, testData.modelName);
  // }
}
