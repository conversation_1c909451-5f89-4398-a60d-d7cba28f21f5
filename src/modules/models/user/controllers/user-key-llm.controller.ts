import { SWAGGER_API_TAGS } from '@/common/swagger';
import { CurrentUser } from '@/modules/auth/decorators';
import { JwtUserGuard } from '@/modules/auth/guards';
import { ApiResponseDto } from '@common/response';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseUUIDPipe,
  Patch,
  Post,
  Query,
  UseGuards
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags
} from '@nestjs/swagger';
import {
  CreateUserKeyLlmDto,
  UpdateUserKeyLlmDto,
  UserKeyLlmQueryDto
} from '../dto/user-key-llm';
import { UserKeyLlmService } from '../services';

/**
 * Controller xử lý API cho User Key LLM
 */
@ApiTags(SWAGGER_API_TAGS.USER_API_KEY_MODEL)
@Controller('key-llm')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class UserKeyLlmController {
  constructor(private readonly userKeyLlmService: UserKeyLlmService) { }

  /**
   * Tạo mới user key LLM
   */
  @Post()
  @ApiOperation({ summary: 'Tạo mới user key LLM' })
  @ApiResponse({
    status: 201,
    description: 'Tạo mới user key LLM thành công',
    type: ApiResponseDto
  })
  create(
    @Body() createDto: CreateUserKeyLlmDto,
    @CurrentUser('id') userId: number
  ): Promise<ApiResponseDto<{ id: string, error?: string }>> {
    return this.userKeyLlmService.create(userId, createDto);
  }

  /**
   * Lấy danh sách user key LLM có phân trang
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách user key LLM có phân trang',
    description: 'API này hỗ trợ tìm kiếm theo tên key, phân trang và sắp xếp'
  })
  @ApiResponse({
    status: 200,
    description: 'Danh sách user key LLM',
    type: ApiResponseDto
  })
  findAll(
    @CurrentUser('id') userId: number,
    @Query() queryDto: UserKeyLlmQueryDto
  ) {
    return this.userKeyLlmService.findAll(userId, queryDto);
  }

  /**
   * Cập nhật user key LLM
   */
  @Patch(':id')
  @ApiOperation({ summary: 'Cập nhật user key LLM' })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật user key LLM thành công',
    type: ApiResponseDto
  })
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateDto: UpdateUserKeyLlmDto,
    @CurrentUser('id') userId: number
  ) {
    return this.userKeyLlmService.update(userId, id, updateDto);
  }

  /**
   * Xóa user key LLM
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa user key LLM' })
  @ApiResponse({
    status: 200,
    description: 'Xóa user key LLM thành công',
    type: ApiResponseDto
  })
  remove(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser('id') userId: number
  ) {
    return this.userKeyLlmService.remove(userId, id);
  }
}
