import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { ModelBase } from '../entities/model-base.entity';
import { PaginatedResult } from '@common/response';
import { ModelBaseQueryDto } from '../admin/dto/model-base';

/**
 * Repository cho ModelBase
 * Xử lý các thao tác CRUD và truy vấn nâng cao liên quan đến model base
 */
@Injectable()
export class ModelBaseRepository extends Repository<ModelBase> {
  private readonly logger = new Logger(ModelBaseRepository.name);

  constructor(private dataSource: DataSource) {
    super(ModelBase, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder c<PERSON> bản cho ModelBase
   * @returns SelectQueryBuilder cho ModelBase
   */
  createBaseQuery(): SelectQueryBuilder<ModelBase> {
    return this.createQueryBuilder('modelBase')
      .select([
        'modelBase.id',
        'modelBase.systemKeyLlmId',
        'modelBase.modelId',
        'modelBase.name',
        'modelBase.description',
        'modelBase.provider',
        'modelBase.maxTokens',
        'modelBase.contextWindow',
        'modelBase.inputCostPer1kTokens',
        'modelBase.outputCostPer1kTokens',
        'modelBase.status',
        'modelBase.isUserAccessible',
        'modelBase.isFineTunable',
        'modelBase.metadata',
        'modelBase.createdAt',
        'modelBase.updatedAt',
        'modelBase.createdBy',
        'modelBase.updatedBy'
      ])
      .where('modelBase.deletedAt IS NULL');
  }

  /**
   * Tìm model base theo ID
   * @param id ID của model base
   * @returns ModelBase hoặc null
   */
  async findById(id: string): Promise<ModelBase | null> {
    return this.createBaseQuery()
      .andWhere('modelBase.id = :id', { id })
      .getOne();
  }

  /**
   * Kiểm tra model base có tồn tại không
   * @param id ID của model base
   * @returns True nếu tồn tại
   */
  async isExists(id: string): Promise<boolean> {
    const count = await this.createBaseQuery()
      .andWhere('modelBase.id = :id', { id })
      .getCount();

    return count > 0;
  }

  /**
   * Kiểm tra model ID đã tồn tại chưa
   * @param modelId Model ID từ provider
   * @param provider Provider
   * @param excludeId ID cần loại trừ (cho update)
   * @returns True nếu đã tồn tại
   */
  async existsByModelId(modelId: string, provider: string, excludeId?: string): Promise<boolean> {
    const query = this.createQueryBuilder('modelBase')
      .where('modelBase.modelId = :modelId', { modelId })
      .andWhere('modelBase.provider = :provider', { provider })
      .andWhere('modelBase.deletedAt IS NULL');

    if (excludeId) {
      query.andWhere('modelBase.id != :excludeId', { excludeId });
    }

    const count = await query.getCount();
    return count > 0;
  }

  /**
   * Kiểm tra tên model đã tồn tại chưa
   * @param name Tên model
   * @param excludeId ID cần loại trừ (cho update)
   * @returns True nếu đã tồn tại
   */
  async existsByName(name: string, excludeId?: string): Promise<boolean> {
    const query = this.createQueryBuilder('modelBase')
      .where('modelBase.name = :name', { name })
      .andWhere('modelBase.deletedAt IS NULL');

    if (excludeId) {
      query.andWhere('modelBase.id != :excludeId', { excludeId });
    }

    const count = await query.getCount();
    return count > 0;
  }

  /**
   * Tìm model base với phân trang
   * @param queryDto Query parameters
   * @returns Kết quả phân trang
   */
  async findWithPagination(queryDto: ModelBaseQueryDto): Promise<PaginatedResult<ModelBase>> {
    const query = this.createBaseQuery();

    // Tìm kiếm theo tên
    if (queryDto.name) {
      query.andWhere('modelBase.name ILIKE :name', {
        name: `%${queryDto.name}%`
      });
    }

    // Lọc theo provider
    if (queryDto.provider) {
      query.andWhere('modelBase.provider = :provider', {
        provider: queryDto.provider
      });
    }

    // Lọc theo user accessible
    if (queryDto.isUserAccessible !== undefined) {
      query.andWhere('modelBase.isUserAccessible = :isUserAccessible', {
        isUserAccessible: queryDto.isUserAccessible
      });
    }

    // Lọc theo fine-tunable
    if (queryDto.isFineTunable !== undefined) {
      query.andWhere('modelBase.isFineTunable = :isFineTunable', {
        isFineTunable: queryDto.isFineTunable
      });
    }

    // Tìm kiếm theo model ID
    if (queryDto.modelId) {
      query.andWhere('modelBase.modelId ILIKE :modelId', {
        modelId: `%${queryDto.modelId}%`
      });
    }

    // Tìm kiếm chung
    if (queryDto.search) {
      query.andWhere(
        '(modelBase.name ILIKE :search OR modelBase.modelId ILIKE :search OR modelBase.description ILIKE :search)',
        { search: `%${queryDto.search}%` }
      );
    }

    // Sắp xếp
    if (queryDto.sortBy) {
      const direction = queryDto.sortDirection || 'ASC';
      query.orderBy(`modelBase.${queryDto.sortBy}`, direction);
    } else {
      query.orderBy('modelBase.createdAt', 'DESC');
    }

    // Phân trang
    const skip = (queryDto.page - 1) * queryDto.limit;
    query.skip(skip).take(queryDto.limit);

    const [items, totalItems] = await query.getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: queryDto.limit,
        totalPages: Math.ceil(totalItems / queryDto.limit),
        currentPage: queryDto.page
      }
    };
  }

  /**
   * Tìm các model base đã xóa với phân trang
   * @param queryDto DTO query
   * @returns Kết quả phân trang
   */
  async findDeletedWithPagination(queryDto: ModelBaseQueryDto): Promise<PaginatedResult<ModelBase>> {
    const query = this.createQueryBuilder('modelBase')
      .select([
        'modelBase.id',
        'modelBase.systemKeyLlmId',
        'modelBase.modelId',
        'modelBase.name',
        'modelBase.description',
        'modelBase.provider',
        'modelBase.maxTokens',
        'modelBase.contextWindow',
        'modelBase.inputCostPer1kTokens',
        'modelBase.outputCostPer1kTokens',
        'modelBase.status',
        'modelBase.isUserAccessible',
        'modelBase.isFineTunable',
        'modelBase.metadata',
        'modelBase.createdAt',
        'modelBase.updatedAt',
        'modelBase.deletedAt',
        'modelBase.createdBy',
        'modelBase.updatedBy',
        'modelBase.deletedBy'
      ])
      .where('modelBase.deletedAt IS NOT NULL');

    // Tìm kiếm theo tên
    if (queryDto.name) {
      query.andWhere('modelBase.name ILIKE :name', {
        name: `%${queryDto.name}%`
      });
    }

    // Sắp xếp theo thời gian xóa
    query.orderBy('modelBase.deletedAt', 'DESC');

    // Phân trang
    const skip = (queryDto.page - 1) * queryDto.limit;
    query.skip(skip).take(queryDto.limit);

    const [items, totalItems] = await query.getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: queryDto.limit,
        totalPages: Math.ceil(totalItems / queryDto.limit),
        currentPage: queryDto.page
      }
    };
  }

  /**
   * Soft delete model base
   * @param id ID của model base
   * @param deletedBy ID người xóa
   * @returns true nếu thành công
   */
  async softDeleteModelBase(id: string, deletedBy: number): Promise<boolean> {
    const result = await this.createQueryBuilder()
      .update(ModelBase)
      .set({
        deletedAt: Date.now(),
        deletedBy
      })
      .where('id = :id', { id })
      .andWhere('deletedAt IS NULL')
      .execute();

    return (result.affected || 0) > 0;
  }

  /**
   * Khôi phục model base đã xóa
   * @param id ID của model base
   * @returns true nếu thành công
   */
  async restoreModelBase(id: string): Promise<boolean> {
    const result = await this.createQueryBuilder()
      .update(ModelBase)
      .set({
        deletedAt: null,
        deletedBy: null
      })
      .where('id = :id', { id })
      .andWhere('deletedAt IS NOT NULL')
      .execute();

    return (result.affected || 0) > 0;
  }

  /**
   * Tìm model base theo provider
   * @param provider Provider
   * @returns Danh sách model base
   */
  async findByProvider(provider: string): Promise<ModelBase[]> {
    return this.createBaseQuery()
      .andWhere('modelBase.provider = :provider', { provider })
      .orderBy('modelBase.name', 'ASC')
      .getMany();
  }

  /**
   * Tìm model base theo system key LLM ID
   * @param systemKeyLlmId System key LLM ID
   * @returns Danh sách model base
   */
  async findBySystemKeyLlmId(systemKeyLlmId: string): Promise<ModelBase[]> {
    return this.createBaseQuery()
      .andWhere('modelBase.systemKeyLlmId = :systemKeyLlmId', { systemKeyLlmId })
      .orderBy('modelBase.name', 'ASC')
      .getMany();
  }

  /**
   * Tìm model base có thể user access
   * @returns Danh sách model base
   */
  async findUserAccessible(): Promise<ModelBase[]> {
    return this.createBaseQuery()
      .andWhere('modelBase.isUserAccessible = :isUserAccessible', { isUserAccessible: true })
      .andWhere('modelBase.status = :status', { status: 'APPROVED' })
      .orderBy('modelBase.name', 'ASC')
      .getMany();
  }

  /**
   * Tìm model base có thể fine-tune
   * @returns Danh sách model base
   */
  async findFineTunable(): Promise<ModelBase[]> {
    return this.createBaseQuery()
      .andWhere('modelBase.isFineTunable = :isFineTunable', { isFineTunable: true })
      .andWhere('modelBase.status = :status', { status: 'APPROVED' })
      .orderBy('modelBase.name', 'ASC')
      .getMany();
  }
}