const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:3000/api/v1';
const JWT_TOKEN = 'your-jwt-token-here'; // Replace with actual JWT token

const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Authorization': `Bearer ${JWT_TOKEN}`,
    'Content-Type': 'application/json'
  }
});

async function checkInventoryStatus() {
  try {
    console.log('🔍 Checking inventory status via API...\n');

    // Check warehouses
    console.log('📦 Checking warehouses...');
    try {
      const warehousesResponse = await api.get('/user/physical-warehouses');
      const warehouses = warehousesResponse.data.result.items;
      console.log(`Found ${warehouses.length} warehouses:`);
      warehouses.forEach(w => {
        console.log(`  - ID: ${w.warehouseId}, Name: ${w.name}, Address: ${w.address}`);
      });
    } catch (error) {
      console.log('❌ Error fetching warehouses:', error.response?.data?.message || error.message);
    }

    // Check products
    console.log('\n🛍️ Checking products...');
    try {
      const productsResponse = await api.get('/user/products?limit=5');
      const products = productsResponse.data.result.items;
      console.log(`Found ${products.length} products:`);
      products.forEach(p => {
        console.log(`  - ID: ${p.id}, Name: ${p.name}`);
      });

      // Check inventory for each product
      if (products.length > 0) {
        console.log('\n📋 Checking inventory for products...');
        for (const product of products.slice(0, 3)) { // Check first 3 products
          try {
            const inventoryResponse = await api.get(`/user/products/${product.id}/inventory`);
            const inventory = inventoryResponse.data.result;
            console.log(`  Product "${product.name}" (ID: ${product.id}):`);
            if (inventory.length > 0) {
              inventory.forEach(inv => {
                console.log(`    - Warehouse: ${inv.warehouse?.name || 'N/A'}, Available: ${inv.availableQuantity}, SKU: ${inv.sku || 'N/A'}`);
              });
            } else {
              console.log(`    - No inventory found`);
            }
          } catch (error) {
            console.log(`    - Error checking inventory for product ${product.id}: ${error.response?.data?.message || error.message}`);
          }
        }
      }
    } catch (error) {
      console.log('❌ Error fetching products:', error.response?.data?.message || error.message);
    }

    // Check general inventory
    console.log('\n📊 Checking general inventory...');
    try {
      const inventoryResponse = await api.get('/user/inventories?limit=10');
      const inventories = inventoryResponse.data.result.items;
      console.log(`Found ${inventories.length} inventory records:`);
      inventories.forEach(inv => {
        console.log(`  - ID: ${inv.id}, Product ID: ${inv.productId}, Warehouse ID: ${inv.warehouseId}, Available: ${inv.availableQuantity}`);
      });
    } catch (error) {
      console.log('❌ Error fetching inventory:', error.response?.data?.message || error.message);
    }

  } catch (error) {
    console.error('❌ General error:', error.message);
  }
}

async function createSampleInventory() {
  try {
    console.log('\n🌱 Creating sample inventory...');

    // Get warehouses and products first
    const warehousesResponse = await api.get('/user/physical-warehouses');
    const warehouses = warehousesResponse.data.result.items;

    const productsResponse = await api.get('/user/products?limit=3');
    const products = productsResponse.data.result.items;

    if (warehouses.length === 0 || products.length === 0) {
      console.log('❌ Cannot create inventory: Need at least 1 warehouse and 1 product');
      return;
    }

    console.log(`Using ${warehouses.length} warehouses and ${products.length} products`);

    // Create inventory for each product in each warehouse
    let createdCount = 0;
    for (const product of products) {
      for (const warehouse of warehouses.slice(0, 2)) { // Use first 2 warehouses
        try {
          const inventoryData = {
            warehouseId: warehouse.warehouseId,
            availableQuantity: Math.floor(Math.random() * 100) + 50, // 50-149
            reservedQuantity: Math.floor(Math.random() * 20), // 0-19
            defectiveQuantity: Math.floor(Math.random() * 10), // 0-9
            sku: `SKU-${product.id}-${warehouse.warehouseId}`,
            barcode: `BC${Date.now()}${product.id}${warehouse.warehouseId}`
          };

          const response = await api.post(`/user/products/${product.id}/inventory`, inventoryData);
          console.log(`✅ Created inventory for product "${product.name}" in warehouse "${warehouse.name}"`);
          console.log(`   Available: ${inventoryData.availableQuantity}, SKU: ${inventoryData.sku}`);
          createdCount++;
        } catch (error) {
          console.log(`❌ Failed to create inventory for product ${product.id} in warehouse ${warehouse.warehouseId}:`);
          console.log(`   ${error.response?.data?.message || error.message}`);
        }
      }
    }

    console.log(`\n🎉 Successfully created ${createdCount} inventory records!`);
  } catch (error) {
    console.error('❌ Error creating sample inventory:', error.response?.data?.message || error.message);
  }
}

// Main execution
async function main() {
  console.log('🚀 Starting inventory check and setup...\n');
  
  await checkInventoryStatus();
  
  console.log('\n' + '='.repeat(50));
  console.log('Would you like to create sample inventory? (This script will attempt to create it)');
  
  // Automatically create sample inventory
  await createSampleInventory();
  
  console.log('\n' + '='.repeat(50));
  console.log('📊 Final status check...\n');
  
  await checkInventoryStatus();
}

main();
