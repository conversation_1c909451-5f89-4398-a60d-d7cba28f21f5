const { DataSource } = require('typeorm');
const path = require('path');

// Database configuration
const dataSource = new DataSource({
  type: 'postgres',
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT) || 5432,
  username: process.env.DB_USERNAME || 'postgres',
  password: process.env.DB_PASSWORD || 'password',
  database: process.env.DB_NAME || 'redai_db',
  synchronize: false,
  logging: true,
});

async function checkAndSeedInventory() {
  try {
    console.log('🔌 Connecting to database...');
    await dataSource.initialize();
    console.log('✅ Database connected successfully');

    // Check existing data
    console.log('\n📊 Checking existing data...');
    
    // Check warehouses
    const warehouses = await dataSource.query(`
      SELECT w.warehouse_id, w.name, w.description, w.type, pw.address, pw.capacity
      FROM warehouse w
      LEFT JOIN physical_warehouse pw ON w.warehouse_id = pw.warehouse_id
      ORDER BY w.warehouse_id
    `);
    console.log(`📦 Found ${warehouses.length} warehouses:`);
    warehouses.forEach(w => {
      console.log(`  - ID: ${w.warehouse_id}, Name: ${w.name}, Type: ${w.type}`);
      if (w.address) console.log(`    Address: ${w.address}, Capacity: ${w.capacity}`);
    });

    // Check products
    const products = await dataSource.query(`
      SELECT id, name, description, created_by, status
      FROM user_products
      WHERE status = 'ACTIVE'
      ORDER BY id
      LIMIT 10
    `);
    console.log(`\n🛍️ Found ${products.length} active products:`);
    products.forEach(p => {
      console.log(`  - ID: ${p.id}, Name: ${p.name}, Created by: ${p.created_by}`);
    });

    // Check inventory
    const inventory = await dataSource.query(`
      SELECT i.id, i.product_id, i.warehouse_id, i.available_quantity, i.current_quantity,
             up.name as product_name, w.name as warehouse_name
      FROM inventory i
      LEFT JOIN user_products up ON i.product_id = up.id
      LEFT JOIN warehouse w ON i.warehouse_id = w.warehouse_id
      ORDER BY i.id
      LIMIT 10
    `);
    console.log(`\n📋 Found ${inventory.length} inventory records:`);
    inventory.forEach(inv => {
      console.log(`  - ID: ${inv.id}, Product: ${inv.product_name}, Warehouse: ${inv.warehouse_name}, Available: ${inv.available_quantity}`);
    });

    // If no inventory, create sample data
    if (inventory.length === 0 && products.length > 0 && warehouses.length > 0) {
      console.log('\n🌱 No inventory found. Creating sample inventory data...');
      
      // Get first user ID from products
      const userId = products[0].created_by;
      console.log(`Using user ID: ${userId}`);

      // Create inventory for first few products in first few warehouses
      const sampleInventory = [];
      const maxProducts = Math.min(3, products.length);
      const maxWarehouses = Math.min(2, warehouses.length);

      for (let i = 0; i < maxProducts; i++) {
        for (let j = 0; j < maxWarehouses; j++) {
          const product = products[i];
          const warehouse = warehouses[j];
          
          const availableQuantity = Math.floor(Math.random() * 100) + 50; // 50-149
          const reservedQuantity = Math.floor(Math.random() * 20); // 0-19
          const defectiveQuantity = Math.floor(Math.random() * 10); // 0-9
          const currentQuantity = availableQuantity + reservedQuantity + defectiveQuantity;
          const totalQuantity = currentQuantity;
          
          const sku = `SKU-${product.id}-${warehouse.warehouse_id}`;
          const barcode = `BC${Date.now()}${i}${j}`;

          sampleInventory.push({
            productId: product.id,
            warehouseId: warehouse.warehouse_id,
            availableQuantity,
            reservedQuantity,
            defectiveQuantity,
            currentQuantity,
            totalQuantity,
            sku,
            barcode,
            lastUpdated: Date.now()
          });
        }
      }

      // Insert inventory records
      for (const inv of sampleInventory) {
        await dataSource.query(`
          INSERT INTO inventory (
            product_id, warehouse_id, available_quantity, reserved_quantity, 
            defective_quantity, current_quantity, total_quantity, sku, barcode, last_updated
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
        `, [
          inv.productId, inv.warehouseId, inv.availableQuantity, inv.reservedQuantity,
          inv.defectiveQuantity, inv.currentQuantity, inv.totalQuantity, 
          inv.sku, inv.barcode, inv.lastUpdated
        ]);
        
        console.log(`✅ Created inventory: Product ${inv.productId} in Warehouse ${inv.warehouseId} - ${inv.availableQuantity} available`);
      }

      console.log(`\n🎉 Successfully created ${sampleInventory.length} inventory records!`);
    } else if (inventory.length > 0) {
      console.log('\n✅ Inventory data already exists. No need to seed.');
    } else {
      console.log('\n⚠️ Cannot create inventory: Missing products or warehouses.');
    }

    // Final summary
    const finalInventoryCount = await dataSource.query('SELECT COUNT(*) as count FROM inventory');
    console.log(`\n📊 Final inventory count: ${finalInventoryCount[0].count}`);

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    if (dataSource.isInitialized) {
      await dataSource.destroy();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the script
checkAndSeedInventory();
